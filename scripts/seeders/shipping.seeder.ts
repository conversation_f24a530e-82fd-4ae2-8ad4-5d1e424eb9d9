/**
 * Shipping Seeder
 * Creates shipping zones and methods for Vietnam
 */

import { BaseSeeder } from "./lib/base-seeder";

export class ShippingSeeder extends BaseSeeder {
  getName(): string {
    return "Shipping";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Create shipping zones for Vietnam regions
    const shippingZonesData = [
      {
        name: "T<PERSON><PERSON> H<PERSON>",
        description: "<PERSON>hu vực TP. H<PERSON> Ch<PERSON> và các quận lân cận",
        provinces: [
          "TP. Hồ Chí Minh",
          "Quận 1", "Quận 2", "Quận 3", "Quận 4", "Quận 5", 
          "Quậ<PERSON> 6", "Quận 7", "Quận 8", "Quận 9", "Quận 10",
          "Quận 11", "Quận 12", "Quận Bình Thạnh", "Quận Gò Vấp",
          "Quận Phú Nhu<PERSON>n", "Quận Tân Bình", "Quận <PERSON> Phú",
          "Quận Thủ Đức", "Huyện Bình Chánh", "Huyện Cần <PERSON>ờ",
          "Huyện Củ Chi", "Huyện H<PERSON>c <PERSON>n", "Huyện Nhà Bè"
        ],
        isActive: true,
      },
      {
        name: "Hà Nội",
        description: "Khu vực Hà Nội và các quận huyện",
        provinces: [
          "Hà Nội",
          "Ba Đình", "Hoàn Kiếm", "Tây Hồ", "Long Biên", "Cầu Giấy",
          "Đống Đa", "Hai Bà Trưng", "Hoàng Mai", "Thanh Xuân",
          "Sóc Sơn", "Đông Anh", "Gia Lâm", "Nam Từ Liêm", "Bắc Từ Liêm",
          "Mê Linh", "Hà Đông", "Sơn Tây", "Ba Vì", "Phúc Thọ",
          "Đan Phượng", "Hoài Đức", "Quốc Oai", "Thạch Thất",
          "Chương Mỹ", "Thanh Oai", "Thường Tín", "Phú Xuyên",
          "Ứng Hòa", "Mỹ Đức"
        ],
        isActive: true,
      },
      {
        name: "Miền Nam",
        description: "Các tỉnh thành miền Nam (trừ TP.HCM)",
        provinces: [
          "An Giang", "Bà Rịa - Vũng Tàu", "Bạc Liêu", "Bến Tre",
          "Bình Dương", "Bình Phước", "Bình Thuận", "Cà Mau",
          "Cần Thơ", "Đồng Nai", "Đồng Tháp", "Hậu Giang",
          "Kiên Giang", "Long An", "Sóc Trăng", "Tây Ninh",
          "Tiền Giang", "Trà Vinh", "Vĩnh Long"
        ],
        isActive: true,
      },
      {
        name: "Miền Trung",
        description: "Các tỉnh thành miền Trung",
        provinces: [
          "Đà Nẵng", "Bình Định", "Bình Thuận", "Đắk Lắk", "Đắk Nông",
          "Gia Lai", "Hà Tĩnh", "Khánh Hòa", "Kon Tum", "Lâm Đồng",
          "Nghệ An", "Ninh Thuận", "Phú Yên", "Quảng Bình", "Quảng Nam",
          "Quảng Ngãi", "Quảng Trị", "Thanh Hóa", "Thừa Thiên Huế"
        ],
        isActive: true,
      },
      {
        name: "Miền Bắc",
        description: "Các tỉnh thành miền Bắc (trừ Hà Nội)",
        provinces: [
          "Bắc Giang", "Bắc Kạn", "Bắc Ninh", "Cao Bằng", "Điện Biên",
          "Hà Giang", "Hà Nam", "Hải Dương", "Hải Phòng", "Hòa Bình",
          "Hưng Yên", "Lai Châu", "Lạng Sơn", "Lào Cai", "Nam Định",
          "Ninh Bình", "Phú Thọ", "Quảng Ninh", "Sơn La", "Thái Bình",
          "Thái Nguyên", "Tuyên Quang", "Vĩnh Phúc", "Yên Bái"
        ],
        isActive: true,
      }
    ];

    const createdZones = [];
    for (const zoneData of shippingZonesData) {
      // Check if zone already exists
      const existing = await this.prisma.shippingZone.findFirst({
        where: { name: zoneData.name }
      });

      let zone;
      if (existing) {
        zone = await this.prisma.shippingZone.update({
          where: { id: existing.id },
          data: zoneData
        });
      } else {
        zone = await this.prisma.shippingZone.create({
          data: zoneData
        });
      }
      createdZones.push(zone);
    }

    // Create shipping methods for each zone
    const createdMethods = [];

    for (const zone of createdZones) {
      let methods = [];

      if (zone.name === "TP. Hồ Chí Minh" || zone.name === "Hà Nội") {
        // Premium zones - more delivery options
        methods = [
          {
            name: "Giao hàng tiêu chuẩn",
            description: "Giao hàng trong 1-2 ngày làm việc",
            type: "STANDARD",
            baseFee: 25000,
            freeShippingMin: 500000,
            estimatedDays: "1-2 ngày",
            maxWeight: 30,
            maxDimensions: { length: 100, width: 100, height: 100 },
            isActive: true,
            sortOrder: 1,
          },
          {
            name: "Giao hàng nhanh",
            description: "Giao hàng trong ngày (đặt trước 15:00)",
            type: "EXPRESS",
            baseFee: 45000,
            freeShippingMin: 800000,
            estimatedDays: "Trong ngày",
            maxWeight: 20,
            maxDimensions: { length: 80, width: 80, height: 80 },
            isActive: true,
            sortOrder: 2,
          },
          {
            name: "Giao hàng trong 2 giờ",
            description: "Giao hàng siêu tốc trong 2 giờ",
            type: "SAME_DAY",
            baseFee: 80000,
            freeShippingMin: 1200000,
            estimatedDays: "2 giờ",
            maxWeight: 10,
            maxDimensions: { length: 50, width: 50, height: 50 },
            isActive: true,
            sortOrder: 3,
          },
          {
            name: "Nhận tại cửa hàng",
            description: "Đến cửa hàng nhận hàng - Miễn phí",
            type: "PICKUP",
            baseFee: 0,
            freeShippingMin: 0,
            estimatedDays: "Sẵn sàng sau 2 giờ",
            maxWeight: null,
            maxDimensions: null,
            isActive: true,
            sortOrder: 4,
          }
        ];
      } else if (zone.name === "Miền Nam" || zone.name === "Miền Trung") {
        // Regional zones
        methods = [
          {
            name: "Giao hàng tiêu chuẩn",
            description: "Giao hàng trong 2-3 ngày làm việc",
            type: "STANDARD",
            baseFee: 35000,
            freeShippingMin: 600000,
            estimatedDays: "2-3 ngày",
            maxWeight: 30,
            maxDimensions: { length: 100, width: 100, height: 100 },
            isActive: true,
            sortOrder: 1,
          },
          {
            name: "Giao hàng nhanh",
            description: "Giao hàng trong 1-2 ngày làm việc",
            type: "EXPRESS",
            baseFee: 55000,
            freeShippingMin: 900000,
            estimatedDays: "1-2 ngày",
            maxWeight: 20,
            maxDimensions: { length: 80, width: 80, height: 80 },
            isActive: true,
            sortOrder: 2,
          }
        ];
      } else {
        // Remote zones (Miền Bắc)
        methods = [
          {
            name: "Giao hàng tiêu chuẩn",
            description: "Giao hàng trong 3-5 ngày làm việc",
            type: "STANDARD",
            baseFee: 45000,
            freeShippingMin: 700000,
            estimatedDays: "3-5 ngày",
            maxWeight: 30,
            maxDimensions: { length: 100, width: 100, height: 100 },
            isActive: true,
            sortOrder: 1,
          },
          {
            name: "Giao hàng nhanh",
            description: "Giao hàng trong 2-3 ngày làm việc",
            type: "EXPRESS",
            baseFee: 65000,
            freeShippingMin: 1000000,
            estimatedDays: "2-3 ngày",
            maxWeight: 20,
            maxDimensions: { length: 80, width: 80, height: 80 },
            isActive: true,
            sortOrder: 2,
          }
        ];
      }

      // Create methods for this zone
      for (const methodData of methods) {
        const method = await this.prisma.shippingMethod.create({
          data: {
            ...methodData,
            zoneId: zone.id,
          }
        });
        createdMethods.push(method);
      }
    }

    this.logSuccess(createdZones.length, "shipping zones");
    console.log(`   └── Created ${createdMethods.length} shipping methods`);

    // Show summary by zone
    for (const zone of createdZones) {
      const methodCount = createdMethods.filter(m => m.zoneId === zone.id).length;
      console.log(`   └── ${zone.name}: ${methodCount} methods`);
    }

    return [...createdZones, ...createdMethods];
  }
}