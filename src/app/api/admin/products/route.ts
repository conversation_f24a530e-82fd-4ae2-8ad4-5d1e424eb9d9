import { NextRequest, NextResponse } from "next/server";
import { container } from "../../di-container";
import { PRODUCT_SERVICE } from "../../services/service-identifiers";
import { ProductService } from "../../services/product.service";
import { initializeSystem } from "../../initialize";
import { verifyAdminAuth } from "../../utils/admin-auth";
import { ProductStatus, AdminRole } from "@prisma/client";

// GET /api/admin/products - <PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm cho admin
export async function GET(request: NextRequest) {
  try {
    // Initialize system if not already done
    initializeSystem();

    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const category = searchParams.get("category");
    const brand = searchParams.get("brand");
    const search = searchParams.get("search");
    const status = searchParams.get("status");
    const featured = searchParams.get("featured");

    // Note: where clause building is now handled by ProductService

    // Get ProductService from DI container
    const productService = container.resolve<ProductService>(PRODUCT_SERVICE);

    // Use ProductService to search products
    const result = await productService.searchProducts({
      search: search || undefined,
      category: category || undefined,
      brand: brand || undefined,
      status: status ? (status as any) : undefined,
      featured:
        featured === "true" ? true : featured === "false" ? false : undefined,
      page,
      limit,
    });

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.hasNext,
        hasPrev: result.hasPrev,
      },
    });
  } catch (error) {
    console.error("Get admin products error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách sản phẩm" },
      { status: 500 }
    );
  }
}

// POST /api/admin/products - Tạo sản phẩm mới
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success || !authResult.admin) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      salePrice,
      sku,
      stock,
      categoryId,
      brandId,
      images, // Array of ProductImage objects with media info
      featured,
      status,
      tags,
      slug,
      attributes,
    } = body;

    // Enhanced validation
    const validationErrors: string[] = [];

    if (!name || name.trim().length === 0) {
      validationErrors.push("Tên sản phẩm là bắt buộc");
    } else if (name.length < 3) {
      validationErrors.push("Tên sản phẩm phải có ít nhất 3 ký tự");
    } else if (name.length > 255) {
      validationErrors.push("Tên sản phẩm không được vượt quá 255 ký tự");
    }

    if (!description || description.trim().length === 0) {
      validationErrors.push("Mô tả sản phẩm là bắt buộc");
    } else if (description.length < 10) {
      validationErrors.push("Mô tả sản phẩm phải có ít nhất 10 ký tự");
    }

    if (!categoryId) {
      validationErrors.push("Danh mục là bắt buộc");
    }

    if (!price || parseFloat(price) <= 0) {
      validationErrors.push("Giá sản phẩm phải lớn hơn 0");
    } else if (parseFloat(price) > 999999999) {
      validationErrors.push("Giá sản phẩm không được vượt quá 999,999,999 VND");
    }

    if (salePrice && parseFloat(salePrice) >= parseFloat(price)) {
      validationErrors.push("Giá khuyến mãi phải nhỏ hơn giá gốc");
    }

    if (stock && parseInt(stock) < 0) {
      validationErrors.push("Số lượng tồn kho không được âm");
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: validationErrors },
        { status: 400 }
      );
    }

    // Initialize system and get ProductService from DI container
    initializeSystem();
    const productService = container.resolve<ProductService>(PRODUCT_SERVICE);

    // Generate SKU if not provided
    const finalSku = sku || `PRD-${Date.now()}`;

    // Create admin user entity for the service
    const adminUser = {
      id: authResult.admin.id,
      role: authResult.admin.role as AdminRole,
      type: "admin" as const,
    };

    // Create product using ProductService
    const product = await productService.createProduct(
      {
        name,
        description,
        price: parseFloat(price),
        salePrice: salePrice ? parseFloat(salePrice) : undefined,
        sku: finalSku,
        stock: parseInt(stock) || 0,
        categoryId,
        brandId: brandId || undefined,
        featured: Boolean(featured),
        status: (status as any) || "ACTIVE",
        tags: tags || [],
        slug,
        attributes: attributes || [],
        mediaIds: images || [],
      },
      adminUser as any
    );

    return NextResponse.json(
      {
        success: true,
        data: product,
        message: "Sản phẩm đã được tạo thành công",
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Create product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo sản phẩm" },
      { status: 500 }
    );
  }
}
