/**
 * <PERSON><PERSON>
 * Creates navigation menus and menu items
 */

import { BaseSeeder } from "./lib/base-seeder";

export class MenuSeeder extends BaseSeeder {
  getName(): string {
    return "Menu";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Create main menus
    let headerMenu = await this.prisma.menu.findFirst({
      where: { location: "header" }
    });
    if (!headerMenu) {
      headerMenu = await this.prisma.menu.create({
        data: {
          name: "Main Navigation",
          location: "header",
          description: "Main website navigation menu",
          isActive: true,
        }
      });
    }

    let footerMenu = await this.prisma.menu.findFirst({
      where: { location: "footer" }
    });
    if (!footerMenu) {
      footerMenu = await this.prisma.menu.create({
        data: {
          name: "Footer Menu",
          location: "footer",
          description: "Footer links menu",
          isActive: true,
        }
      });
    }

    // Get categories for menu items
    const categories = await this.prisma.category.findMany({
      where: { parentId: null }, // Only parent categories
      take: 6,
    });

    const createdMenus = [headerMenu, footerMenu];
    let totalMenuItems = 0;

    // Create header menu items
    const headerMenuItems = [
      {
        title: "Trang chủ",
        url: "/",
        type: "LINK",
        order: 1,
        icon: "home",
        isActive: true,
      },
      {
        title: "Sản phẩm",
        url: "/products",
        type: "LINK",
        order: 2,
        icon: "shopping-bag",
        isActive: true,
      },
      {
        title: "Thương hiệu",
        url: "/brands",
        type: "LINK",
        order: 3,
        icon: "award",
        isActive: true,
      },
      {
        title: "Tin tức",
        url: "/blog",
        type: "LINK",
        order: 4,
        icon: "newspaper",
        isActive: true,
      },
      {
        title: "Liên hệ",
        url: "/contact",
        type: "LINK",
        order: 5,
        icon: "phone",
        isActive: true,
      },
    ];

    for (const item of headerMenuItems) {
      await this.prisma.menuItem.create({
        data: {
          ...item,
          menuId: headerMenu.id,
        },
      });
      totalMenuItems++;
    }

    // Create category submenu under "Sản phẩm"
    const productsMenuItem = await this.prisma.menuItem.findFirst({
      where: {
        menuId: headerMenu.id,
        title: "Sản phẩm",
      },
    });

    if (productsMenuItem && categories.length > 0) {
      for (let i = 0; i < Math.min(categories.length, 6); i++) {
        const category = categories[i];
        await this.prisma.menuItem.create({
          data: {
            menuId: headerMenu.id,
            parentId: productsMenuItem.id,
            title: category.name,
            url: `/categories/${category.slug}`,
            type: "CATEGORY",
            order: i + 1,
            isActive: true,
          },
        });
        totalMenuItems++;
      }
    }

    // Create footer menu items
    const footerMenuItems = [
      {
        title: "Về chúng tôi",
        url: "/about-us",
        type: "PAGE",
        order: 1,
        isActive: true,
      },
      {
        title: "Chính sách bảo mật",
        url: "/privacy-policy",
        type: "PAGE",
        order: 2,
        isActive: true,
      },
      {
        title: "Điều khoản sử dụng",
        url: "/terms-of-service",
        type: "PAGE",
        order: 3,
        isActive: true,
      },
      {
        title: "Chính sách đổi trả",
        url: "/return-policy",
        type: "PAGE",
        order: 4,
        isActive: true,
      },
      {
        title: "Hướng dẫn mua hàng",
        url: "/shopping-guide",
        type: "PAGE",
        order: 5,
        isActive: true,
      },
      {
        title: "Câu hỏi thường gặp",
        url: "/faq",
        type: "PAGE",
        order: 6,
        isActive: true,
      },
    ];

    for (const item of footerMenuItems) {
      await this.prisma.menuItem.create({
        data: {
          ...item,
          menuId: footerMenu.id,
        },
      });
      totalMenuItems++;
    }

    // Create mobile menu
    let mobileMenu = await this.prisma.menu.findFirst({
      where: { location: "mobile" }
    });
    if (!mobileMenu) {
      mobileMenu = await this.prisma.menu.create({
        data: {
          name: "Mobile Menu",
          location: "mobile",
          description: "Mobile navigation menu",
          isActive: true,
        }
      });
    }

    createdMenus.push(mobileMenu);

    // Create mobile menu items (simplified)
    const mobileMenuItems = [
      {
        title: "Trang chủ",
        url: "/",
        type: "LINK",
        order: 1,
        icon: "home",
        isActive: true,
      },
      {
        title: "Danh mục",
        url: "/categories",
        type: "LINK",
        order: 2,
        icon: "grid",
        isActive: true,
      },
      {
        title: "Tìm kiếm",
        url: "/search",
        type: "LINK",
        order: 3,
        icon: "search",
        isActive: true,
      },
      {
        title: "Giỏ hàng",
        url: "/cart",
        type: "LINK",
        order: 4,
        icon: "shopping-cart",
        isActive: true,
      },
      {
        title: "Tài khoản",
        url: "/account",
        type: "LINK",
        order: 5,
        icon: "user",
        isActive: true,
      },
    ];

    for (const item of mobileMenuItems) {
      await this.prisma.menuItem.create({
        data: {
          ...item,
          menuId: mobileMenu.id,
        },
      });
      totalMenuItems++;
    }

    this.logSuccess(createdMenus.length, "menus");
    console.log(`   └── Created ${totalMenuItems} menu items`);

    return createdMenus;
  }
}
