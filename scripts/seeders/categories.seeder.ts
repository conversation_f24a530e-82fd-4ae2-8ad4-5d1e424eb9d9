/**
 * Categories Seeder
 * Creates hierarchical category structure
 */

import { BaseSeeder } from "./lib/base-seeder";

export class CategoriesSeeder extends BaseSeeder {
  getName(): string {
    return "Categories";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Get category media
    const categoryMedia = await this.prisma.media.findMany({
      where: { 
        folder: "categories",
        type: "EXTERNAL",
        isActive: true
      },
    });

    const categoryStructure = [
      {
        name: "<PERSON>o thun",
        slug: "ao-thun",
        description: "<PERSON>o thun nam nữ đa dạng kiểu dáng",
        subcategories: [
          "Áo thun nam basic",
          "Áo thun nữ form rộng",
          "Áo thun polo",
          "Áo thun graphic",
          "Áo thun vintage",
        ],
      },
      {
        name: "<PERSON><PERSON><PERSON> đầm",
        slug: "vay-dam",
        description: "<PERSON><PERSON><PERSON> đầm nữ thanh lịch và thời trang",
        subcategories: [
          "<PERSON><PERSON><PERSON> maxi",
          "<PERSON><PERSON><PERSON> suông",
          "<PERSON><PERSON><PERSON> xòe",
          "Đầm bodycon",
          "<PERSON><PERSON>y midi",
        ],
      },
      {
        name: "Quần jeans",
        slug: "quan-jeans",
        description: "Quần jeans nam nữ đa dạng phong cách",
        subcategories: [
          "Quần jeans skinny",
          "Quần jeans boyfriend",
          "Quần jeans rách",
          "Quần jeans ống rộng",
          "Quần jeans vintage",
        ],
      },
      {
        name: "Áo khoác",
        slug: "ao-khoac",
        description: "Áo khoác thời trang cho mọi mùa",
        subcategories: [
          "Áo khoác bomber",
          "Áo blazer",
          "Áo khoác denim",
          "Áo cardigan",
          "Áo hoodie",
        ],
      },
      {
        name: "Phụ kiện",
        slug: "phu-kien",
        description: "Phụ kiện thời trang hoàn thiện phong cách",
        subcategories: [
          "Túi xách",
          "Ví cầm tay",
          "Thắt lưng",
          "Khăn choàng",
          "Mũ nón",
          "Kính mát",
        ],
      },
      {
        name: "Giày dép",
        slug: "giay-dep",
        description: "Giày dép thời trang cho mọi dịp",
        subcategories: [
          "Giày sneaker",
          "Giày cao gót",
          "Giày oxford",
          "Dép sandal",
          "Giày boot",
        ],
      },
    ];

    const createdCategories = [];

    // Create main categories
    for (const categoryData of categoryStructure) {
      // Find media for this category
      const mainImage = categoryMedia.find(
        (m) =>
          m.filename.includes(categoryData.slug) && m.filename.includes("main")
      );
      
      // Verify image exists if found
      let validImageId = null;
      if (mainImage) {
        const imageExists = await this.prisma.media.findFirst({
          where: { id: mainImage.id, isActive: true }
        });
        validImageId = imageExists ? mainImage.id : null;
      }

      const mainCategory = await this.upsertRecord(
        this.prisma.category,
        { slug: categoryData.slug },
        {
          name: categoryData.name,
          slug: categoryData.slug,
          description: categoryData.description,
          imageId: validImageId,
        }
      );
      createdCategories.push(mainCategory);

      // Create subcategories
      for (let i = 0; i < categoryData.subcategories.length; i++) {
        const subName = categoryData.subcategories[i];
        const subSlug = this.dataGenerator.generateSlug(subName) as string;

        // Verify parent category exists
        const parentExists = await this.prisma.category.findFirst({
          where: { id: mainCategory.id }
        });
        
        if (!parentExists) {
          this.logWarning(`Parent category not found for ${subName}`);
          continue;
        }
        
        const subcategory = await this.upsertRecord(
          this.prisma.category,
          { slug: subSlug },
          {
            name: subName,
            slug: subSlug,
            description: `${subName} chất lượng cao`,
            parentId: mainCategory.id,
          }
        );
        createdCategories.push(subcategory);
      }
    }

    this.logSuccess(createdCategories.length, "categories");
    return createdCategories;
  }
}
